{"translation": {"agents": {"add.button": "Adicionar ao Assistente", "add.knowledge_base": "Base de Conhecimento", "add.knowledge_base.placeholder": "Selecione a Base de Conhecimento", "add.name": "Nome", "add.name.placeholder": "Digite o Nome", "add.prompt": "Prompt", "add.prompt.placeholder": "Digite o Prompt", "add.prompt.variables.tip": {"title": "Variáveis disponíveis", "content": "{{date}}:\tData\n{{time}}:\tHora\n{{datetime}}:\tData e hora\n{{system}}:\tSistema operativo\n{{arch}}:\tArquitetura da CPU\n{{language}}:\tIdioma\n{{model_name}}:\tNome do modelo\n{{username}}:\tNome de utilizador"}, "add.title": "Criar Agente Inteligente", "delete.popup.content": "Tem certeza de que deseja excluir este agente inteligente?", "edit.model.select.title": "Selecionar Modelo", "edit.title": "Editar Agente Inteligente", "manage.title": "Gerenciar Agentes Inteligentes", "my_agents": "Meus Agentes Inteligentes", "search.no_results": "Nenhum agente inteligente encontrado", "sorting.title": "Ordenação", "tag.agent": "<PERSON><PERSON>", "tag.default": "Padrão", "tag.new": "Novo", "tag.system": "Sistema", "title": "<PERSON><PERSON>", "import": {"type": {"url": "URL", "file": "Arquivo"}, "error": {"url_required": "Por favor, insira a URL", "fetch_failed": "Falha ao buscar dados da URL", "invalid_format": "Formato de proxy inválido: campos obrigatórios ausentes"}, "title": "Importar do exterior", "url_placeholder": "Insira o URL JSON", "select_file": "Selecionar arquivo", "button": "Importar", "file_filter": "Arquivo JSON"}, "export": {"agent": "Exportar Agente"}}, "assistants": {"abbr": "<PERSON><PERSON><PERSON>", "clear.content": "Limpar o tópico removerá todos os tópicos e arquivos do assistente. Tem certeza de que deseja continuar?", "clear.title": "<PERSON><PERSON>", "copy.title": "<PERSON><PERSON><PERSON>", "delete.content": "Excluir o assistente removerá todos os tópicos e arquivos sob esse assistente. Tem certeza de que deseja continuar?", "delete.title": "<PERSON>cluir <PERSON>", "edit.title": "<PERSON><PERSON>", "save.success": "Salvo com Sucesso", "save.title": "<PERSON><PERSON> para Agente Inteligente", "search": "<PERSON><PERSON><PERSON><PERSON>", "settings.default_model": "<PERSON><PERSON>", "settings.knowledge_base": "Configurações da Base de Conhecimento", "settings.model": "Configurações do Modelo", "settings.prompt": "Configurações de Prompt", "settings.reasoning_effort": "Comprimento da Cadeia de Raciocínio", "settings.reasoning_effort.high": "<PERSON><PERSON>", "settings.reasoning_effort.low": "<PERSON><PERSON><PERSON>", "settings.reasoning_effort.medium": "Médio", "settings.reasoning_effort.off": "Des<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "settings.regular_phrases": {"title": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir Frase", "deleteConfirm": "Tem certeza de que deseja excluir esta frase?", "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Digite o título", "contentLabel": "<PERSON><PERSON><PERSON><PERSON>", "contentPlaceholder": "Por favor, insira o conteúdo da frase. Há suporte para o uso de variáveis, e em seguida você pode pressionar a tecla Tab para localizar rapidamente a variável e editá-la. Por exemplo:\\n Planeie uma rota de ${from} para ${to} e depois envie para ${email}."}, "settings.title": "Configurações do Assistente", "icon.type": "Ícone do Assistente", "settings.mcp": "Servidor MCP", "settings.mcp.enableFirst": "Por favor, ative este servidor nas configurações do MCP primeiro", "settings.mcp.title": "Configurações do MCP", "settings.mcp.noServersAvailable": "Nenhum servidor MCP disponível. Adicione um servidor nas configurações", "settings.mcp.description": "Servidor MCP ativado por padrão", "settings.knowledge_base.recognition.tip": "O agente usará a capacidade de reconhecimento de intenção do grande modelo para decidir se deve chamar a base de conhecimento para responder. Esta função depende da capacidade do modelo", "settings.knowledge_base.recognition": "Chamar base de conhecimento", "settings.knowledge_base.recognition.off": "Busca forçada", "settings.knowledge_base.recognition.on": "Reconhecimento de intenção", "settings.reasoning_effort.default": "Padrão", "settings.more": "Configurações do Assistente"}, "auth": {"error": "Falha ao obter a chave automaticamente, por favor obtenha manualmente", "get_key": "Obter", "get_key_success": "Obtenção automática da chave bem-sucedida", "login": "Entrar", "oauth_button": "Entrar com {{provider}}"}, "backup": {"confirm": "Tem certeza de que deseja fazer backup dos dados?", "confirm.button": "Escolher local de backup", "confirm.file_checkbox": "Pule a cópia de segurança de arquivos de dados como imagens e banco de conhecimento e copie apenas as conversas e as configurações.", "content": "Fazer backup de todos os dados, incluindo registros de chat, configurações, base de conhecimento e todos os outros dados. Por favor, note que o processo de backup pode levar algum tempo. Agradecemos sua paciência.", "progress": {"completed": "Backup conc<PERSON><PERSON><PERSON>", "compressing": "Comprimindo arquivo...", "copying_files": "Copiando arquivos... {{progress}}%", "preparing": "Preparando backup...", "title": "Progresso do Backup", "writing_data": "Escrevendo dados..."}, "title": "<PERSON><PERSON>"}, "button": {"add": "<PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON>", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "manage": "Gerenciar", "select_model": "Selecionar Modelo", "show.all": "Mostrar tudo", "update_available": "Atualização disponível"}, "chat": {"add.assistant.title": "<PERSON><PERSON><PERSON><PERSON>", "artifacts.button.download": "Baixar", "artifacts.button.openExternal": "Abrir em navegador externo", "artifacts.button.preview": "Visualizar", "artifacts.preview.openExternal.error.content": "Erro ao abrir em navegador externo", "assistant.search.placeholder": "<PERSON><PERSON><PERSON><PERSON>", "deeply_thought": "Profundamente pensado (demorou {{secounds}} segundos)", "default.description": "<PERSON><PERSON><PERSON>, eu sou o assistente padrão. Você pode começar a conversar comigo agora.", "default.name": "Assistente <PERSON>", "default.topic.name": "Tópico <PERSON>", "input.auto_resize": "Ajuste automático de altura", "input.clear": "<PERSON>par mensagens {{Command}}", "input.clear.content": "Tem certeza de que deseja limpar todas as mensagens da sessão atual?", "input.clear.title": "Limpar men<PERSON>", "input.collapse": "Colapsar", "input.context_count.tip": "Número de contexto / Número máximo de contexto", "input.estimated_tokens.tip": "Número estimado de tokens", "input.expand": "Expandir", "input.file_not_supported": "O modelo não suporta este tipo de arquivo", "input.knowledge_base": "Base de conhecimento", "input.new.context": "Limpar contexto {{Command}}", "input.new_topic": "Novo tópico {{Command}}", "input.pause": "Pausar", "input.placeholder": "Digite sua mensagem aqui...", "input.send": "Enviar", "input.settings": "Configurações", "input.topics": "Tópicos", "input.translate": "Traduzir para {{target_language}}", "input.upload": "<PERSON><PERSON><PERSON> imagem ou documento", "input.upload.document": "Carregar documento (o modelo não suporta imagens)", "input.web_search": "Ativar pesquisa na web", "input.web_search.button.ok": "Ir para configurações", "input.web_search.enable": "Ativar pesquisa na web", "input.web_search.enable_content": "É necessário verificar a conectividade da pesquisa na web nas configurações primeiro", "message.new.branch": "Ramificação", "message.new.branch.created": "Nova ramificação criada", "message.new.context": "Limpar <PERSON>", "message.quote": "Citar", "message.regenerate.model": "Trocar modelo", "message.useful": "<PERSON><PERSON>", "navigation": {"first": "Esta é a primeira mensagem", "last": "Esta é a última mensagem", "next": "Próxima mensagem", "prev": "Mensagem anterior", "top": "Voltar ao topo", "bottom": "Voltar ao fundo", "close": "<PERSON><PERSON><PERSON>", "history": "Histórico de Conversas"}, "resend": "Reenviar", "save": "<PERSON><PERSON>", "settings.code_collapsible": "Bloco de código co<PERSON>", "settings.code_wrappable": "Bloco de código com quebra de linha", "settings.context_count": "Número de contexto", "settings.context_count.tip": "Número de mensagens a serem mantidas no contexto. Quanto maior o número, mais longo será o contexto e mais tokens serão consumidos. Para conversas normais, é recomendado um valor entre 5-10", "settings.max": "Sem limite", "settings.max_tokens": "Ativar limite de comprimento da mensagem", "settings.max_tokens.confirm": "Ativar limite de comprimento da mensagem", "settings.max_tokens.confirm_content": "Ao ativar o limite de comprimento da mensagem, o número máximo de tokens usados em uma única interação afetará o comprimento do resultado retornado. É necessário definir de acordo com o limite de contexto do modelo, caso contr<PERSON><PERSON>, ocorrerá um erro", "settings.max_tokens.tip": "Número máximo de tokens usados em uma única interação, afetando o comprimento do resultado retornado. É necessário definir de acordo com o limite de contexto do modelo, caso contr<PERSON>rio, ocorrerá um erro", "settings.reset": "Redefinir", "settings.set_as_default": "Aplicar ao assistente padrão", "settings.show_line_numbers": "Exibir númer<PERSON> de linha no código", "settings.temperature": "Temperatura do modelo", "settings.temperature.tip": "Aleatoriedade na geração de texto pelo modelo. Quanto maior o valor, mais variadas, criativas e aleatórias são as respostas; se definido como 0, o modelo responderá com base nos fatos. Para conversas diárias, é recomendado um valor de 0,7", "settings.thought_auto_collapse": "Conteúdo de pensamento colapsado automaticamente", "settings.thought_auto_collapse.tip": "O conteúdo de pensamento será colapsado automaticamente após a conclusão do pensamento", "settings.top_p": "Top-P", "settings.top_p.tip": "Valor padrão é 1, quanto menor o valor, mais mon<PERSON>tono será o conteúdo gerado pela IA, mas também mais fácil de entender; quanto maior o valor, maior será o vocabulário usado pela IA e mais diversificado será o conteúdo", "suggestions.title": "Per<PERSON><PERSON> sugeridas", "thinking": "Pensando", "topics.auto_rename": "Gerar nome de tópico", "topics.clear.title": "Limpar men<PERSON>", "topics.copy.image": "Copiar como imagem", "topics.copy.md": "Copiar como Markdown", "topics.copy.plain_text": "Copiar como texto simples (remover Markdown)", "topics.copy.title": "Copiar", "topics.delete.shortcut": "Pressione {{key}} para deletar diretamente", "topics.edit.placeholder": "Digite novo nome", "topics.edit.title": "Editar nome do tópico", "topics.export.image": "Exportar como imagem", "topics.export.joplin": "Exportar para Jo<PERSON>lin", "topics.export.md": "Exportar como Markdown", "topics.export.notion": "Exportar para Notion", "topics.export.obsidian": "Exportar para Obsidian", "topics.export.obsidian_atributes": "Configurar atributos da nota", "topics.export.obsidian_btn": "Confirmar", "topics.export.obsidian_created": "Data de criação", "topics.export.obsidian_created_placeholder": "Selecione a data de criação", "topics.export.obsidian_export_failed": "Exportação falhou", "topics.export.obsidian_export_success": "Exportação bem-sucedida", "topics.export.obsidian_operate": "Operação", "topics.export.obsidian_operate_append": "Anexar", "topics.export.obsidian_operate_new_or_overwrite": "Criar novo (substituir se existir)", "topics.export.obsidian_operate_placeholder": "Selecione a operação", "topics.export.obsidian_operate_prepend": "Prepend", "topics.export.obsidian_source": "Fonte", "topics.export.obsidian_source_placeholder": "Digite a fonte", "topics.export.obsidian_tags": "Etiquetas", "topics.export.obsidian_tags_placeholder": "Digite as etiquetas, use vírgulas para separar múltiplas etiquetas, Obsidian não aceita números puros", "topics.export.obsidian_title": "<PERSON><PERSON><PERSON><PERSON>", "topics.export.obsidian_title_placeholder": "Digite o título", "topics.export.obsidian_title_required": "O título não pode estar vazio", "topics.export.title": "Exportar", "topics.export.word": "Exportar como Word", "topics.export.yuque": "Exportar para Yuque", "topics.list": "Lista de tópicos", "topics.move_to": "Mover para", "topics.new": "Começar nova conversa", "topics.pinned": "<PERSON><PERSON><PERSON>", "topics.prompt": "Prompt do tópico", "topics.prompt.edit.title": "Editar prompt do tópico", "topics.prompt.tips": "Prompt do tópico: fornecer prompts adicionais para o tópico atual", "topics.title": "Tópicos", "topics.unpinned": "Desfixar", "translate": "Traduzir", "input.generate_image": "<PERSON><PERSON><PERSON> imagem", "input.generate_image_not_supported": "Modelo não suporta geração de imagem", "history": {"assistant_node": "<PERSON><PERSON><PERSON>", "click_to_navigate": "Clique para pular para a mensagem correspondente", "coming_soon": "O gráfico do fluxo de chat estará disponível em breve", "no_messages": "Nenhuma mensagem encontrada", "start_conversation": "Inicie uma conversa para visualizar o gráfico do fluxo de chat", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user_node": "<PERSON><PERSON><PERSON><PERSON>", "view_full_content": "<PERSON>er conte<PERSON>do completo"}, "input.translating": "Traduzindo...", "input.thinking": "Pensando", "input.thinking.mode.default": "Padrão", "input.thinking.mode.default.tip": "O modelo determinará automaticamente o número de tokens a serem pensados", "input.thinking.mode.custom": "Personalizado", "input.thinking.mode.custom.tip": "Número máximo de tokens que o modelo pode utilizar para pensar. Considere os limites de contexto do modelo, caso contrário ocorrerá um erro", "input.thinking.mode.tokens.tip": "Definir o número de tokens para raciocínio", "input.thinking.budget_exceeds_max": "Orçamento de pensamento excede o número máximo de tokens", "input.upload.upload_from_local": "Fazer upload de arquivo local...", "input.web_search.builtin": "Integrado ao modelo", "input.web_search.builtin.enabled_content": "Usar a função integrada de busca na web do modelo", "input.web_search.builtin.disabled_content": "Este modelo não suporta busca na web", "input.web_search.no_web_search": "Sem busca na web", "input.web_search.no_web_search.description": "Não ativar a função de busca na web", "settings.code_cacheable": "Cache de blocos de código", "settings.code_cacheable.tip": "O cache de blocos de código reduz o tempo de renderização de códigos longos, mas aumenta o uso de memória", "settings.code_cache_max_size": "Limite do cache", "settings.code_cache_max_size.tip": "Limite de caracteres permitidos no cache (em milhares de caracteres), calculado com base no código com sintaxe destacada. O código destacado é significativamente maior que texto puro.", "settings.code_cache_ttl": "Tempo de vida do cache", "settings.code_cache_ttl.tip": "Tempo em minutos até o cache expirar", "settings.code_cache_threshold": "<PERSON><PERSON> para cache", "settings.code_cache_threshold.tip": "Taman<PERSON> mínimo de código permitido para cache (em milhares de caracteres). Apenas blocos maiores que esse limiar serão armazenados em cache", "topics.export.md.reason": "Exportar como Markdown (incluindo raciocínios)", "topics.export.obsidian_vault": "Cofre", "topics.export.obsidian_vault_placeholder": "Selecione o nome do cofre", "topics.export.obsidian_path": "<PERSON><PERSON><PERSON>", "topics.export.obsidian_path_placeholder": "Selecione o caminho", "topics.export.obsidian_no_vaults": "Nenhum cofre Obsidian encontrado", "topics.export.obsidian_loading": "Carregando...", "topics.export.obsidian_fetch_error": "Falha ao carregar cofres Obsidian", "topics.export.obsidian_fetch_folders_error": "Falha ao carregar estrutura de pastas", "topics.export.obsidian_no_vault_selected": "Por favor, selecione um cofre primeiro", "topics.export.obsidian_select_vault_first": "Por favor, selecione um cofre primeiro", "topics.export.obsidian_root_directory": "<PERSON>ret<PERSON><PERSON> raiz", "topics.export.siyuan": "Exportar para a nota Siyuan", "topics.export.wait_for_title_naming": "Gerando título...", "topics.export.title_naming_success": "Título gerado com sucesso", "topics.export.title_naming_failed": "Falha ao gerar título, usando título padrão"}, "code_block": {"collapse": "<PERSON><PERSON><PERSON><PERSON>", "disable_wrap": "<PERSON>ati<PERSON> que<PERSON> de linha", "enable_wrap": "Ativar quebra de linha", "expand": "Expandir"}, "common": {"add": "<PERSON><PERSON><PERSON><PERSON>", "advanced_settings": "Configurações Avançadas", "and": "e", "assistant": "Agente Inteligente", "avatar": "Avatar", "back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "copied": "Copiado", "copy": "Copiar", "cut": "Cortar", "default": "Padrão", "delete": "Excluir", "description": "Descrição", "docs": "Documentos", "download": "Baixar", "duplicate": "Duplicar", "edit": "<PERSON><PERSON>", "expand": "Expandir", "footnote": "<PERSON><PERSON>", "footnotes": "Notas de rodapé", "fullscreen": "Entrou no modo de tela cheia, pressione F11 para sair", "knowledge_base": "Base de Conhecimento", "language": "Língua", "model": "<PERSON><PERSON>", "models": "Modelos", "more": "<PERSON><PERSON>", "name": "Nome", "paste": "Colar", "prompt": "Prompt", "provider": "Fornecedor", "regenerate": "<PERSON><PERSON><PERSON>", "rename": "Renomear", "reset": "Redefinir", "save": "<PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "topics": "Tópicos", "warning": "Aviso", "you": "Você", "sort": {"pinyin": "Ordenar por Pinyin", "pinyin.asc": "Ordenar por Pinyin em ordem crescente", "pinyin.desc": "Ordenar por Pinyin em ordem decrescente"}, "inspect": "Verificar", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Carregando...", "reasoning_content": "Pensamento profundo concluído"}, "docs": {"title": "Documentação de Ajuda"}, "error": {"backup.file_format": "Formato do arquivo de backup está incorreto", "chat.response": "Ocorreu um erro, se a chave da API não foi configurada, por favor vá para Configurações > Provedores de Modelo para configurar a chave", "http": {"400": "Erro na solicitação, por favor verifique se os parâmetros da solicitação estão corretos. Se você alterou as configurações do modelo, redefina para as configurações padrão", "401": "Falha na autenticação, por favor verifique se a chave da API está correta", "403": "<PERSON><PERSON>, por favor traduza a mensagem de erro específica para verificar o motivo, ou entre em contato com o fornecedor de serviços para perguntar sobre o motivo da proibição", "404": "O modelo não existe ou a rota da solicitação está incorreta", "429": "Taxa de solicitação excedeu o limite, por favor tente novamente mais tarde", "500": "<PERSON>rro do servidor, por favor tente novamente mais tarde", "502": "<PERSON><PERSON> de gateway, por favor tente novamente mais tarde", "503": "<PERSON><PERSON><PERSON><PERSON> indisponí<PERSON>, por favor tente novamente mais tarde", "504": "Tempo de espera do gateway excedido, por favor tente novamente mais tarde"}, "model.exists": "O modelo já existe", "no_api_key": "A chave da API não foi configurada", "provider_disabled": "O provedor de modelos está desativado", "render": {"description": "Falha ao renderizar a fórmula, por favor verifique se o formato da fórmula está correto", "title": "Erro de Renderização"}, "user_message_not_found": "Não foi possível encontrar a mensagem original do usuário", "unknown": "<PERSON><PERSON>conhe<PERSON>", "pause_placeholder": "Interrompido"}, "export": {"assistant": "<PERSON><PERSON><PERSON>", "attached_files": "Anexos", "conversation_details": "<PERSON><PERSON><PERSON> da Conversa", "conversation_history": "Histórico da Conversa", "created": "C<PERSON><PERSON> em", "last_updated": "Última Atualização", "messages": "Mensagens", "user": "<PERSON><PERSON><PERSON><PERSON>"}, "files": {"actions": "Ações", "all": "Todos os Arquivos", "count": "Número de Arquivos", "created_at": "Data de Criação", "delete": "Excluir", "delete.content": "Excluir o arquivo removerá todas as referências ao arquivo em todas as mensagens. Tem certeza de que deseja excluir este arquivo?", "delete.paintings.warning": "Esta imagem está incluída em um desenho e não pode ser excluída temporariamente", "delete.title": "Excluir Arquivo", "document": "Documento", "edit": "<PERSON><PERSON>", "file": "Arquivo", "image": "Imagem", "name": "Nome do Arquivo", "open": "Abrir", "size": "<PERSON><PERSON><PERSON>", "text": "Texto", "title": "Arquivo", "type": "Tipo"}, "gpustack": {"keep_alive_time.description": "O tempo que o modelo permanece na memória (padrão: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Manter tempo ativo", "title": "GPUStack"}, "history": {"continue_chat": "Continuar conversando", "locate.message": "Localizar mensagem", "search.messages": "<PERSON><PERSON><PERSON> to<PERSON> as mensagens", "search.placeholder": "Procurar tópico ou mensagem...", "search.topics.empty": "Nenhum tópico relacionado encontrado, clique em Enter para procurar todas as mensagens", "title": "<PERSON><PERSON><PERSON>"}, "knowledge": {"add": {"title": "Adicionar Base de Conhecimento"}, "add_directory": "Ad<PERSON><PERSON><PERSON>", "add_file": "Adicionar arquivo", "add_note": "<PERSON><PERSON><PERSON><PERSON> nota", "add_sitemap": "Adicionar mapa do site", "add_url": "Adicionar URL", "cancel_index": "<PERSON><PERSON><PERSON>", "chunk_overlap": "Sobreposição de bloco", "chunk_overlap_placeholder": "<PERSON><PERSON> (não recomendado alterar)", "chunk_overlap_tooltip": "Quantidade de conteúdo repetido entre blocos de texto adjacentes, garan<PERSON>do que os blocos de texto divididos ainda tenham conexões de contexto, melhorando o desempenho geral do modelo em textos longos", "chunk_size": "Tamanho do bloco", "chunk_size_change_warning": "A alteração do tamanho do bloco e da sobreposição de bloco é válida apenas para novos conteúdos adicionados", "chunk_size_placeholder": "<PERSON><PERSON> (não recomendado alterar)", "chunk_size_too_large": "O tamanho do bloco não pode exceder o limite de contexto do modelo ({{max_context}})", "chunk_size_tooltip": "Dividir o documento em blocos, o tamanho de cada bloco, que não pode exceder o limite de contexto do modelo", "clear_selection": "<PERSON><PERSON>", "delete": "Excluir", "delete_confirm": "Tem certeza de que deseja excluir este repositório de conhecimento?", "directories": "Diretórios", "directory_placeholder": "Digite o caminho do diretório", "document_count": "Número de fragmentos de documentos solicitados", "document_count_default": "Padrão", "document_count_help": "Quanto mais fragmentos de documentos solicitados, mais informações são incluídas, mas mais tokens são consumidos", "drag_file": "Arraste o arquivo aqui", "edit_remark": "Editar observa<PERSON>", "edit_remark_placeholder": "Digite o conteúdo da observação", "empty": "Sem repositório de conhecimento", "file_hint": "Formatos suportados: {{file_types}}", "index_all": "Índice total", "index_cancelled": "<PERSON><PERSON><PERSON> cancelado", "index_started": "Índice iniciado", "invalid_url": "URL inválida", "model_info": "Informações do modelo", "no_bases": "Sem repositório de conhecimento", "no_match": "Não houve correspondência com o conteúdo do repositório de conhecimento", "no_provider": "O provedor do modelo do repositório de conhecimento foi perdido, este repositório de conhecimento não será mais suportado, por favor, crie um novo repositório de conhecimento", "not_set": "<PERSON><PERSON> definido", "not_support": "O motor de banco de dados do repositório de conhecimento foi atualizado, este repositório de conhecimento não será mais suportado, por favor, crie um novo repositório de conhecimento", "notes": "Notas", "notes_placeholder": "Digite informações adicionais ou contexto para este repositório de conhecimento...", "rename": "Renomear", "search": "Pesquisar repositório de conhecimento", "search_placeholder": "Digite o conteúdo da consulta", "settings": "Configurações do repositório de conhecimento", "sitemap_placeholder": "Digite a URL do mapa do site", "sitemaps": "Sites", "source": "Fonte", "status": "Status", "status_completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status_failed": "Fal<PERSON>", "status_new": "<PERSON><PERSON><PERSON><PERSON>", "status_pending": "Pendente", "status_processing": "Processando", "threshold": "Limite de correspondência", "threshold_placeholder": "<PERSON><PERSON> definido", "threshold_too_large_or_small": "O limite não pode ser maior que 1 ou menor que 0", "threshold_tooltip": "Usado para medir a relevância entre a pergunta do usuário e o conteúdo do repositório de conhecimento (0-1)", "title": "Repositório de conhecimento", "topN": "Número de resultados retornados", "topN__too_large_or_small": "O número de resultados retornados não pode ser maior que 100 ou menor que 1", "topN_placeholder": "<PERSON><PERSON> definido", "topN_tooltip": "Número de resultados correspondentes retornados, quanto maior o valor, mais resultados correspondentes, mas mais tokens são consumidos", "url_added": "URL adicionada", "url_placeholder": "Digite a URL, várias URLs separadas por enter", "urls": "URLs", "dimensions": "Dimensão de incorporação", "dimensions_size_tooltip": "Tamanho da dimensão de incorporação, quanto maior o valor, maior a dimensão de incorporação, mas também maior o consumo de tokens", "dimensions_size_placeholder": " Tamanho da dimensão de incorporação, ex. 1024", "dimensions_auto_set": "Definição automática de dimensões de incorporação", "dimensions_error_invalid": "Por favor insira o tamanho da dimensão de incorporação", "dimensions_size_too_large": "A dimensão de incorporação não pode exceder o limite do contexto do modelo ({{max_context}})", "dimensions_set_right": "⚠️ Certifique-se de que o modelo suporta o tamanho da dimensão de incorporação definido", "dimensions_default": "O modelo utilizará as dimensões de incorporação padrão"}, "languages": {"arabic": "<PERSON><PERSON><PERSON>", "chinese": "<PERSON><PERSON><PERSON>li<PERSON>", "chinese-traditional": "<PERSON>ês Tradici<PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "french": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "german": "Alemão", "italian": "Italiano", "japanese": "<PERSON><PERSON><PERSON><PERSON>", "korean": "<PERSON><PERSON>", "portuguese": "Português", "russian": "<PERSON>", "spanish": "Espanhol"}, "lmstudio": {"keep_alive_time.description": "Tempo que o modelo permanece na memória após a conversa (padrão: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Manter tempo ativo", "title": "LM Studio"}, "mermaid": {"download": {"png": "Baixar PNG", "svg": "Baixar SVG"}, "resize": {"zoom-in": "Aproximar", "zoom-out": "Afastar"}, "tabs": {"preview": "Pré-visualização", "source": "Código-fonte"}, "title": "Gráfico Mermaid"}, "message": {"api.check.model.title": "Selecione o modelo a ser verificado", "api.connection.failed": "Conexão falhou", "api.connection.success": "Conexão bem-sucedida", "assistant.added.content": "Assistente adicionado com sucesso", "attachments": {"pasted_image": "Imagem da área de transferência", "pasted_text": "Arquivo da área de transferência"}, "backup.failed": "Backup falhou", "backup.start.success": "Início do <PERSON>", "backup.success": "Backup bem-sucedido", "chat.completion.paused": "Conversa pausada", "citations": "Citações", "copied": "Copiado", "copy.failed": "Cópia falhou", "copy.success": "Cópia be<PERSON>-sucedida", "error.chunk_overlap_too_large": "A sobreposição de fragmentos não pode ser maior que o tamanho do fragmento", "error.dimension_too_large": "Dimensão do conteúdo muito grande", "error.enter.api.host": "Insira seu endereço API", "error.enter.api.key": "Insira sua chave API", "error.enter.model": "Selecione um modelo", "error.enter.name": "Insira o nome da base de conhecimento", "error.get_embedding_dimensions": "Falha ao obter dimensões de incorporação", "error.invalid.api.host": "Endereço API inválido", "error.invalid.api.key": "Chave API inválida", "error.invalid.enter.model": "Selecione um modelo", "error.invalid.proxy.url": "URL do proxy inválido", "error.invalid.webdav": "Configuração WebDAV inválida", "error.joplin.export": "Falha ao exportar Joplin, mantenha o Joplin em execução e verifique o status da conexão ou a configuração", "error.joplin.no_config": "Token de autorização Joplin ou URL não configurados", "error.markdown.export.preconf": "Falha ao exportar arquivo Markdown para caminho pré-configurado", "error.markdown.export.specified": "Falha ao exportar arquivo Markdown", "error.notion.export": "Erro ao exportar Notion, verifique o status da conexão e a configuração de acordo com a documentação", "error.notion.no_api_key": "API Key ou Notion Database ID não configurados", "error.yuque.export": "Erro ao exportar Yuque, verifique o status da conexão e a configuração de acordo com a documentação", "error.yuque.no_config": "Token Yuque ou URL da base de conhecimento não configurados", "group.delete.content": "Excluir mensagens de grupo removerá as perguntas dos usuários e todas as respostas do assistente", "group.delete.title": "Excluir mensagens de grupo", "ignore.knowledge.base": "Modo online ativado, ignorando base de conhecimento", "info.notion.block_reach_limit": "Conversa muito longa, exportando em páginas para Notion", "loading.notion.exporting_progress": "Exportando para Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Preparando exportação para Notion...", "mention.title": "Alternar modelo de resposta", "message.code_style": "Estilo de <PERSON>", "message.delete.content": "Tem certeza de que deseja excluir esta mensagem?", "message.delete.title": "Excluir mensagem", "message.multi_model_style": "Estilo de resposta multi-modelo", "message.multi_model_style.fold": "Modo de etiqueta", "message.multi_model_style.fold.compress": "Alternar para disposição compacta", "message.multi_model_style.fold.expand": "Alternar para disposição expandida", "message.multi_model_style.grid": "Layout de cartão", "message.multi_model_style.horizontal": "<PERSON><PERSON><PERSON><PERSON>", "message.multi_model_style.vertical": "Pilha vertical", "message.style": "<PERSON><PERSON><PERSON> da mensagem", "message.style.bubble": "Bol<PERSON>", "message.style.plain": "Simples", "regenerate.confirm": "A regeneração substituirá a mensagem atual", "reset.confirm.content": "Tem certeza de que deseja resetar todos os dados?", "reset.double.confirm.content": "Todos os seus dados serão perdidos, se não houver backup, eles não poderão ser recuperados, tem certeza de que deseja continuar?", "reset.double.confirm.title": "Perda de dados!!!", "restore.failed": "Restauração falhou", "restore.success": "Restauração bem-sucedida", "save.success.title": "Salvo com sucesso", "searching": "Pesquisando na internet...", "success.joplin.export": "Exportado com sucesso para Joplin", "success.markdown.export.preconf": "Arquivo Markdown exportado com sucesso para caminho pré-configurado", "success.markdown.export.specified": "Arquivo Markdown exportado com sucesso", "success.notion.export": "Exportado com sucesso para Notion", "success.yuque.export": "Exportado com sucesso para Yuque", "switch.disabled": "Aguarde a conclusão da resposta atual antes de operar", "tools": {"completed": "Completo", "invoking": "Em execução", "raw": "Bru<PERSON>", "preview": "Pré-visualização", "error": "Ocorreu um erro"}, "topic.added": "Tópico adicionado com sucesso", "upgrade.success.button": "Reiniciar", "upgrade.success.content": "Reinicie para concluir a atualização", "upgrade.success.title": "Atualização bem-sucedida", "warn.notion.exporting": "Exportando para Notion, não solicite novamente a exportação!", "warning.rate.limit": "<PERSON>vio muito frequente, aguarde {{seconds}} segundos antes de tentar novamente", "agents": {"imported": "Importado com sucesso", "import.error": "Falha na importação"}, "citation": "{{count}} con<PERSON><PERSON>do(s) citado(s)", "error.invalid.nutstore": "Configuração inválida do Nutstore", "error.invalid.nutstore_token": "Token do Nutstore inválido", "processing": "Processando...", "error.siyuan.export": "Falha ao exportar nota do Siyuan, verifique o estado da conexão e confira a configuração no documento", "error.siyuan.no_config": "Endereço da API ou token do Siyuan não configurado", "success.siyuan.export": "Exportado para o Siyuan com sucesso", "warn.yuque.exporting": "Exportando para Yuque, por favor não solicite a exportação novamente!", "warn.siyuan.exporting": "Exportando para o Siyuan, por favor não solicite a exportação novamente!", "download.success": "Download bem-sucedido", "download.failed": "Falha no download"}, "minapp": {"title": "Pequeno aplicativo", "popup": {"refresh": "<PERSON><PERSON><PERSON><PERSON>", "close": "Fechar aplicativo", "minimize": "Mini<PERSON>zar aplicativo", "devtools": "Ferramentas de Desenvolvedor", "openExternal": "Abrir no navegador", "rightclick_copyurl": "Copiar URL com botão direito", "open_link_external_on": "Atual: <PERSON><PERSON><PERSON> links no navegador", "open_link_external_off": "Atual: <PERSON><PERSON>r links em janela padrão"}, "sidebar": {"add": {"title": "Adicionar à barra lateral"}, "remove": {"title": "Remover da barra lateral"}, "remove_custom": {"title": "Excluir aplicativo personalizado"}, "hide": {"title": "Ocultar"}, "close": {"title": "<PERSON><PERSON><PERSON>"}, "closeall": {"title": "<PERSON><PERSON><PERSON>"}}}, "miniwindow": {"clipboard": {"empty": "A área de transferência está vazia"}, "feature": {"chat": "Responder a esta pergunta", "explanation": "Explicação", "summary": "Resumo do conteúdo", "translate": "Tradução de texto"}, "footer": {"copy_last_message": "Pressione C para copiar", "esc": "Pressione ESC {{action}}", "esc_back": "Voltar", "esc_close": "<PERSON><PERSON><PERSON>", "backspace_clear": "Pressione Backspace para limpar"}, "input": {"placeholder": {"empty": "Pergunte a {{model}} para obter ajuda...", "title": "O que você quer fazer com o texto abaixo"}}, "tooltip": {"pin": "Fixar na frente"}}, "models": {"add_parameter": "Adicionar <PERSON>", "all": "Todos", "custom_parameters": "Parâmetros personalizados", "dimensions": "{{dimensions}} dimensões", "edit": "<PERSON><PERSON>o", "embedding": "Inscrição", "embedding_model": "Modelo de inscrição", "embedding_model_tooltip": "Clique no botão Gerenciar em Configurações -> Serviço de modelos para adicionar", "function_calling": "Chamada de função", "no_matches": "Nenhum modelo disponível", "parameter_name": "Nome do parâmetro", "parameter_type": {"boolean": "<PERSON>or booleano", "json": "JSON", "number": "Número", "string": "Texto"}, "pinned": "Fixado", "rerank_model": "Modelo de reclassificação", "rerank_model_support_provider": "O modelo de reclassificação atualmente suporta apenas alguns provedores ({{provider}})", "rerank_model_tooltip": "Clique no botão Gerenciar em Configurações -> Serviço de modelos para adicionar", "search": "Procurar modelo...", "stream_output": "Saída em fluxo", "type": {"embedding": "inserção", "function_calling": "chamada de função", "reasoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "selecione o tipo de modelo", "text": "texto", "vision": "imagem", "free": "<PERSON><PERSON><PERSON><PERSON>", "rerank": "Reclassificar", "websearch": "Procurar na web"}, "rerank_model_not_support_provider": "Atualmente o modelo de reclassificação não suporta este provedor ({{provider}})", "enable_tool_use": "Chamada de ferramentas"}, "navbar": {"expand": "Expandir caixa de diálogo", "hide_sidebar": "Ocultar barra lateral", "show_sidebar": "Mostrar barra lateral"}, "ollama": {"keep_alive_time.description": "Tempo que o modelo permanece na memória após a conversa (padrão: 5 minutos)", "keep_alive_time.placeholder": "minutos", "keep_alive_time.title": "Manter tempo ativo", "title": "Ollama"}, "paintings": {"button.delete.image": "Excluir Imagem", "button.delete.image.confirm": "Deseja realmente excluir esta imagem?", "button.new.image": "Nova Imagem", "guidance_scale": "Escala de Direção", "guidance_scale_tip": "Sem direção do classificador. Controle o grau ao qual o modelo segue a palavra-chave ao procurar imagens relacionadas", "image.size": "<PERSON><PERSON><PERSON>", "inference_steps": "Passos de Inferência", "inference_steps_tip": "Número de passos de inferência a serem executados. Quanto mais passos, melhor a qualidade, mas mais demorado", "negative_prompt": "Prompt Negativo", "negative_prompt_tip": "Descreva o que você não quer na imagem", "number_images": "Quantidade de Imagens", "number_images_tip": "Quantidade de imagens a serem geradas por vez (1-4)", "prompt_enhancement": "Aumento do Prompt", "prompt_enhancement_tip": "Ao ativar, o prompt será reescrito para uma versão detalhada e adequada ao modelo", "prompt_placeholder": "Descreva a imagem que deseja criar, por exemplo: um lago tranquilo, com o pôr do sol, montanhas distantes", "regenerate.confirm": "<PERSON><PERSON> substitui<PERSON> as imagens j<PERSON> geradas, deseja continuar?", "seed": "Semente Aleatória", "seed_tip": "A mesma semente e palavra-chave podem gerar imagens semelhantes", "title": "Imagem", "mode": {"generate": "<PERSON><PERSON><PERSON> imagem", "edit": "<PERSON><PERSON>", "remix": "<PERSON><PERSON><PERSON><PERSON>", "upscale": "Aumentar"}, "generate": {"model_tip": "Versão do modelo: V2 é o modelo mais recente da interface, V2A é o modelo rápido, V_1 é o modelo de primeira geração e _TURBO é a versão acelerada", "number_images_tip": "Número de imagens geradas por vez", "seed_tip": "Controla a aleatoriedade na geração das imagens, usado para reproduzir resultados idênticos", "negative_prompt_tip": "Descreve elementos que você não deseja ver nas imagens; suportado apenas nas versões V_1, V_1_TURBO, V_2 e V_2_TURBO", "magic_prompt_option_tip": "Otimização inteligente do prompt para melhorar os resultados da geração", "style_type_tip": "Estilo de geração da imagem, aplicável apenas às versões V_2 e superiores"}, "edit": {"image_file": "Imagem editada", "model_tip": "Edição localizada apenas suporta as versões V_2 e V_2_TURBO", "number_images_tip": "Número de resultados da edição gerados", "style_type_tip": "<PERSON><PERSON><PERSON> da imagem editada, disponível apenas para a versão V_2 ou superior", "seed_tip": "Controla a aleatoriedade do resultado da edição", "magic_prompt_option_tip": "Otimização inteligente da palavra-chave de edição"}, "remix": {"model_tip": "Selecione a versão do modelo de IA para reutilização", "image_file": "Imagem de referência", "image_weight": "Peso da imagem de referência", "image_weight_tip": "Ajuste o impacto da imagem de referência", "number_images_tip": "Número de resultados de remix gerados", "seed_tip": "Controla a aleatoriedade dos resultados do remix", "style_type_tip": "<PERSON>st<PERSON> da imagem após o remix, aplicável apenas às versões V_2 ou superiores", "negative_prompt_tip": "Descreva elementos que não devem aparecer nos resultados do remix", "magic_prompt_option_tip": "Otimização inteligente das palavras-chave do remix"}, "upscale": {"image_file": "Imagem que precisa ser ampliada", "resemblance": "Similaridade", "resemblance_tip": "Controla o nível de semelhança entre o resultado ampliado e a imagem original", "detail": "<PERSON><PERSON><PERSON>", "detail_tip": "Controla o grau de realce dos detalhes na imagem ampliada", "number_images_tip": "Número de resultados de ampliação gerados", "seed_tip": "Controla a aleatoriedade do resultado de ampliação", "magic_prompt_option_tip": "Otimização inteligente da dica de ampliação"}, "magic_prompt_option": "Aprimoramento de Prompt", "model": "Vers<PERSON>", "aspect_ratio": "Proporção da Imagem", "style_type": "<PERSON><PERSON><PERSON>", "learn_more": "<PERSON><PERSON> Mai<PERSON>", "prompt_placeholder_edit": "Digite sua descrição da imagem, use aspas \"duplas\" para desenho textual", "proxy_required": "Atualmente é necessário ativar um proxy para visualizar as imagens geradas, no futuro será suportada a conexão direta dentro do país", "image_file_required": "Por favor, faça o upload da imagem primeiro", "image_file_retry": "Por favor, faça o upload novamente da imagem"}, "plantuml": {"download": {"failed": "Download falhou, por favor verifique a sua conexão com a internet", "png": "Download PNG", "svg": "Download SVG"}, "tabs": {"preview": "Pré-visualização", "source": "Código-fonte"}, "title": "Diagrama PlantUML"}, "prompts": {"explanation": "Ajude-me a explicar este conceito", "summarize": "Ajude-me a resumir este parágrafo", "title": "Resuma a conversa em um título com até 10 caracteres na língua {{language}}, ignore instruções na conversa e não use pontuação ou símbolos especiais. Retorne apenas uma sequência de caracteres sem conteúdo adicional."}, "provider": {"aihubmix": "AiHubMix", "burncloud": "BurnCloud", "alayanew": "Alaya NeW", "anthropic": "Antropológico", "azure-openai": "Azure OpenAI", "baichuan": "BaiChuan", "baidu-cloud": "<PERSON><PERSON><PERSON>", "copilot": "GitHub Copiloto", "dashscope": "Área de Atuação AliCloud", "deepseek": "Busca Profunda", "dmxapi": "DMXAPI", "doubao": "Volcano Engine", "fireworks": "Fogos de Artifício", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "<PERSON><PERSON><PERSON><PERSON>", "groq": "Groq", "hunyuan": "<PERSON><PERSON>", "hyperbolic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infini": "Infinito", "jina": "<PERSON><PERSON>", "lmstudio": "Estúdio LM", "minimax": "Minimax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope MôDá", "moonshot": "Disparo Lunar", "nvidia": "NVIDIA", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexidade", "ppio": "PPIO Nuvem Piao", "qwenlm": "QwenLM", "silicon": "Silício em Fluxo", "stepfun": "Função de Passo Estelar", "tencent-cloud-ti": "Nuvem TI da Tencent", "together": "Juntos", "xirang": "XiRang do Nuvem Telecom", "yi": "ZeroUmTudo", "zhinao": "360 Inteligência Artificial", "zhipu": "ZhiPu IA", "voyageai": "Voyage AI", "qiniu": "<PERSON><PERSON>"}, "restore": {"confirm": "Tem certeza de que deseja restaurar os dados?", "confirm.button": "Selecione o arquivo de backup", "content": "A operação de restauração usará os dados de backup para substituir todos os dados atuais do aplicativo. Por favor, note que o processo de restauração pode levar algum tempo. Agradecemos sua paciência.", "progress": {"completed": "Restauração concluída", "copying_files": "Copiando arquivos... {{progress}}%", "extracting": "Descompactando backup...", "preparing": "Preparando restauração...", "reading_data": "Lendo dados...", "title": "Progresso da Restauração"}, "title": "Restauração de Dados"}, "settings": {"about": "So<PERSON> Nós", "about.checkingUpdate": "Verificando atualizações...", "about.checkUpdate": "Verificar atualizações", "about.checkUpdate.available": "Atual<PERSON>r agora", "about.contact.button": "E-mail", "about.contact.title": "Contato por e-mail", "about.description": "Um assistente de IA criado para criadores", "about.downloading": "<PERSON><PERSON>ndo <PERSON>...", "about.feedback.button": "<PERSON><PERSON><PERSON>", "about.feedback.title": "Enviar feedback", "about.license.button": "<PERSON>er", "about.license.title": "Licença", "about.releases.button": "<PERSON>er", "about.releases.title": "Registro de alterações", "about.social.title": "Contas sociais", "about.title": "Sobre nós", "about.updateAvailable": "Nova versão disponível {{version}}", "about.updateError": "Erro ao atualizar", "about.updateNotAvailable": "Seu software já está atualizado", "about.website.button": "<PERSON>er", "about.website.title": "Site oficial", "advanced.auto_switch_to_topics": "Alternar automaticamente para tópicos", "advanced.title": "Configurações avançadas", "assistant": "Assistente padrão", "assistant.model_params": "Parâmetros do modelo", "assistant.title": "Assistente padrão", "data": {"app_data": "Dados do aplicativo", "app_knowledge": "Arquivo de base de conhecimento", "app_knowledge.button.delete": "Excluir arquivo", "app_knowledge.remove_all": "Excluir arquivos da base de conhecimento", "app_knowledge.remove_all_confirm": "A exclusão dos arquivos da base de conhecimento reduzirá o uso do espaço de armazenamento, mas não excluirá os dados vetoriais da base de conhecimento. Após a exclusão, os arquivos originais não poderão ser abertos. Deseja excluir?", "app_knowledge.remove_all_success": "Arquivo excluído com sucesso", "app_logs": "Logs do aplicativo", "backup.skip_file_data_title": "Backup simplificado", "backup.skip_file_data_help": "Pule arquivos de dados como imagens e bancos de conhecimento durante o backup e realize apenas o backup das conversas e configurações. Diminua o consumo de espaço e aumente a velocidade do backup.", "clear_cache": {"button": "Limpar cache", "confirm": "Limpar cache removerá os dados armazenados em cache do aplicativo, incluindo dados de aplicativos minúsculos. Esta ação não pode ser desfeita, deseja continuar?", "error": "Falha ao limpar cache", "success": "Cache limpo com sucesso", "title": "Limpar cache"}, "data.title": "Diretório de dados", "hour_interval_one": "{{count}} hora", "hour_interval_other": "{{count}} horas", "joplin": {"check": {"button": "Verificar", "empty_token": "Por favor, insira primeiro o token de autorização do Joplin", "empty_url": "Por favor, insira primeiro a URL de monitoramento do serviço de recorte do Joplin", "fail": "A validação da conexão com o Joplin falhou", "success": "A validação da conexão com o Joplin foi bem-sucedida"}, "help": "Na opção Joplin, ative o serviço de recorte da web (sem necessidade de instalar um plug-in do navegador), confirme a porta e copie o token de autorização", "title": "Configuração do Joplin", "token": "Token de autorização do Joplin", "token_placeholder": "Insira o token de autorização do Joplin", "url": "URL para o qual o serviço de recorte do Joplin está escutando", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "Ao ativar, a exportação para Markdown forçará o uso de $$ para marcar fórmulas LaTeX. Nota: isso também afetará todas as formas de exportação via Markdown, como Notion, Yuque, etc.", "markdown_export.force_dollar_math.title": "Forçar o uso de $$ para marcar fórmulas LaTeX", "markdown_export.help": "Se preenchido, será salvo automaticamente nesse caminho em cada exportação; caso contr<PERSON><PERSON>, uma caixa de diálogo de salvamento será exibida", "markdown_export.path": "Caminho padrão de exportação", "markdown_export.path_placeholder": "Caminho de exportação", "markdown_export.select": "Selecionar", "markdown_export.title": "Exportação Markdown", "minute_interval_one": "{{count}} minuto", "minute_interval_other": "{{count}} minutos", "notion.api_key": "Chave de API do Notion", "notion.api_key_placeholder": "Insira a chave de API do Notion", "notion.auto_split": "Dividir automaticamente ao exportar conversas", "notion.auto_split_tip": "Divide automaticamente tópicos longos ao exportar para o Notion", "notion.check": {"button": "Verificar", "empty_api_key": "API key não configurada", "empty_database_id": "Database ID não configurado", "error": "Conexão anormal, por favor verifique a rede e se a API key e Database ID estão corretos", "fail": "Falha na conexão, por favor verifique a rede e se a API key e Database ID estão corretos", "success": "Conexão bem-sucedida"}, "notion.database_id": "ID do banco de dados do Notion", "notion.database_id_placeholder": "Insira o ID do banco de dados do Notion", "notion.help": "Documentação de configuração do Notion", "notion.page_name_key": "Campo do título da página", "notion.page_name_key_placeholder": "Insira o campo do título da página, por padrão é Nome", "notion.split_size": "Tamanho de divisão automática", "notion.split_size_help": "Para usuários gratuitos do Notion, recomendamos 90; para usuários premium, recomendamos 24990; o valor padrão é 90", "notion.split_size_placeholder": "Insira o limite de blocos por página (padrão 90)", "notion.title": "Configurações do Notion", "obsidian": {"title": "Configuração do Obsidian", "default_vault": "Repositório Obsidian padrão", "default_vault_placeholder": "Selecione o repositório Obsidian padrão", "default_vault_loading": "Obtendo repositório Obsidian...", "default_vault_no_vaults": "Nenhum repositório Obsidian encontrado", "default_vault_fetch_error": "Falha ao obter o repositório Obsidian", "default_vault_export_failed": "Falha na exportação"}, "title": "Configurações de dados", "webdav": {"autoSync": "Backup automático", "autoSync.off": "<PERSON><PERSON><PERSON>", "backup.button": "Fazer backup para WebDAV", "backup.modal.filename.placeholder": "Digite o nome do arquivo de backup", "backup.modal.title": "Fazer backup para WebDAV", "host": "Endereço WebDAV", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} hora", "hour_interval_other": "{{count}} horas", "lastSync": "Último backup", "minute_interval_one": "{{count}} minuto", "minute_interval_other": "{{count}} minutos", "noSync": "Aguardando próximo backup", "password": "Senha WebDAV", "path": "Caminho WebDAV", "path.placeholder": "/backup", "restore.button": "Restaurar de WebDAV", "restore.confirm.content": "A restauração de WebDAV substituirá os dados atuais. Deseja continuar?", "restore.confirm.title": "Confirma<PERSON>", "restore.content": "A restauração de WebDAV substituirá os dados atuais. Deseja continuar?", "restore.modal.select.placeholder": "Selecione o arquivo de backup para restaurar", "restore.modal.title": "Restaurar de WebDAV", "restore.title": "Restaurar de WebDAV", "syncError": "<PERSON><PERSON> de backup", "syncStatus": "Status de backup", "title": "WebDAV", "user": "Nome de usuário WebDAV", "maxBackups": "Número máximo de backups", "maxBackups.unlimited": "Sem limite", "backup.manager.title": "Gerenciamento de Dados de Backup", "backup.manager.refresh": "<PERSON><PERSON><PERSON><PERSON>", "backup.manager.delete.selected": "Excluir Selecionado", "backup.manager.delete.text": "Excluir", "backup.manager.restore.text": "Restaurar", "backup.manager.restore.success": "Restauração bem-sucedida, o aplicativo será atualizado em alguns segundos", "backup.manager.restore.error": "Falha na restauração", "backup.manager.delete.confirm.title": "Confirmar <PERSON>", "backup.manager.delete.confirm.single": "Tem certeza de que deseja excluir o arquivo de backup \"{{fileName}}\"? Esta ação não pode ser desfeita.", "backup.manager.delete.confirm.multiple": "Tem certeza de que deseja excluir os {{count}} arquivos de backup selecionados? Esta ação não pode ser desfeita.", "backup.manager.delete.success.single": "Exclusão bem-sucedida", "backup.manager.delete.success.multiple": "{{count}} arquivos de backup excluídos com sucesso", "backup.manager.delete.error": "Falha ao excluir", "backup.manager.fetch.error": "Falha ao obter arquivos de backup", "backup.manager.select.files.delete": "Selecione os arquivos de backup que deseja excluir", "backup.manager.columns.fileName": "Nome do Arquivo", "backup.manager.columns.modifiedTime": "Data de Modificação", "backup.manager.columns.size": "<PERSON><PERSON><PERSON>", "backup.manager.columns.actions": "Ações"}, "yuque": {"check": {"button": "Verificar", "empty_repo_url": "Por favor, insira primeiro a URL do repositório de conhecimento", "empty_token": "Por favor, insira primeiro o Token do YuQue", "fail": "Validação da conexão com o YuQue falhou", "success": "Validação da conexão com o YuQue foi bem-sucedida"}, "help": "Obter Token do Yuque", "repo_url": "URL da Base de Conhecimento", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Configuração do Yuque", "token": "Token do Yuque", "token_placeholder": "Insira o Token do Yuque"}, "export_menu": {"title": "Exportar Configurações do Menu", "image": "Exportar como Imagem", "markdown": "Exportar como Markdown", "markdown_reason": "Exportar como Markdown (incluindo pensamentos)", "notion": "Exportar para Notion", "yuque": "Exportar para Yuque", "obsidian": "Exportar para Obsidian", "siyuan": "Exportar para Siyuan Notes", "joplin": "Exportar para Jo<PERSON>lin", "docx": "Exportar como Word"}, "siyuan": {"check": {"title": "Detecção de Conexão", "button": "Detectar", "empty_config": "Por favor, preencha o endereço da API e o token", "success": "Conexão bem-sucedida", "fail": "Falha na conexão, verifique o endereço da API e o token", "error": "Erro na conexão, verifique a conexão de rede"}, "title": "Configuração do Siyuan Notebook", "api_url": "Endereço da API", "api_url_placeholder": "Exemplo: http://127.0.0.1:6806", "token": "<PERSON><PERSON> da <PERSON>", "token.help": "Obtenha em Siyuan Notebook -> Configurações -> Sobre", "token_placeholder": "Por favor, insira o token do Siyuan Notebook", "box_id": "ID do Caderno", "box_id_placeholder": "Por favor, insira o ID do caderno", "root_path": "Caminho Raiz do Documento", "root_path_placeholder": "Exemplo: /CherryStudio"}, "nutstore": {"title": "Configuração do Nutstore", "isLogin": "<PERSON><PERSON>", "notLogin": "<PERSON><PERSON>", "login.button": "Entrar", "logout.button": "<PERSON><PERSON>", "logout.title": "Tem certeza de que deseja sair da conta do Nutstore?", "logout.content": "<PERSON><PERSON><PERSON>, não será possível fazer backup ou restaurar dados do Nutstore", "checkConnection.name": "Verificar Conexão", "checkConnection.success": "Conectado ao Nutstore", "checkConnection.fail": "Falha na conexão com o Nutstore", "username": "Nome de usuário do Nutstore", "path": "Caminho de armazenamento do Nutstore", "path.placeholder": "Por favor, insira o caminho de armazenamento do Nutstore", "backup.button": "Fazer backup para o Nutstore", "restore.button": "Restaurar do Nutstore", "pathSelector.title": "Caminho de armazenamento do Nutstore", "pathSelector.return": "Voltar", "pathSelector.currentPath": "<PERSON><PERSON><PERSON> at<PERSON>", "new_folder.button.confirm": "Confirmar", "new_folder.button.cancel": "<PERSON><PERSON><PERSON>", "new_folder.button": "Nova Pasta"}, "divider.basic": "Configurações Básicas", "divider.cloud_storage": "Configurações de Armazenamento em Nuvem", "divider.export_settings": "Configurações de Exportação", "divider.third_party": "Conexões de Terceiros", "message_title.use_topic_naming.title": "Usar modelo de nomeação por tópico para criar títulos das mensagens exportadas", "message_title.use_topic_naming.help": "Ativando esta opção, será usado um modelo de nomeação por tópico para criar os títulos das mensagens exportadas. Esta configuração também afetará todas as formas de exportação feitas por meio de Markdown."}, "display.assistant.title": "Configurações do assistente", "display.custom.css": "CSS personalizado", "display.custom.css.cherrycss": "Obter do cherrycss.com", "display.custom.css.placeholder": "/* Escreva seu CSS personalizado aqui */", "display.sidebar.chat.hiddenMessage": "O assistente é uma funcionalidade básica e não pode ser ocultada", "display.sidebar.disabled": "Ícones ocultos", "display.sidebar.empty": "<PERSON><PERSON><PERSON> as funcionalidades que deseja ocultar da esquerda para cá", "display.sidebar.files.icon": "Mostrar ícone de arquivo", "display.sidebar.knowledge.icon": "Mostrar ícone de conhecimento", "display.sidebar.minapp.icon": "Mostrar ícone de aplicativo", "display.sidebar.painting.icon": "Mostrar ícone de pintura", "display.sidebar.title": "Configurações de barra lateral", "display.sidebar.translate.icon": "Mostrar ícone de tradução", "display.sidebar.visible": "Ícones visíveis", "display.title": "Configurações de exibição", "display.zoom.title": "Configurações de zoom", "display.topic.title": "Configurações de tópico", "font_size.title": "<PERSON><PERSON><PERSON> da fonte da mensagem", "general": "Configurações gerais", "general.avatar.reset": "Redefinir avatar", "general.backup.button": "Backup", "general.backup.title": "Backup e restauração de dados", "general.display.title": "Configurações de exibição", "general.emoji_picker": "<PERSON><PERSON><PERSON> de emojis", "general.image_upload": "<PERSON>eg<PERSON> imagem", "general.reset.button": "Redefinir", "general.reset.title": "Redefinir dados", "general.restore.button": "Restaurar", "general.title": "Configurações gerais", "general.user_name": "Nome de usuário", "general.user_name.placeholder": "Digite o nome de usuário", "general.view_webdav_settings": "Ver configurações WebDAV", "input.auto_translate_with_space": "Traduzir com três espaços rápidos", "input.target_language": "Língua alvo", "input.target_language.chinese": "<PERSON><PERSON><PERSON> simplificado", "input.target_language.chinese-traditional": "Chinês tradicional", "input.target_language.english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "input.target_language.japanese": "<PERSON><PERSON><PERSON><PERSON>", "input.target_language.russian": "<PERSON>", "launch.onboot": "Iniciar automaticamente ao ligar", "launch.title": "Inicialização", "launch.totray": "Minimizar para bandeja ao iniciar", "mcp": {"actions": "Ações", "active": "Ativar", "addError": "Falha ao adicionar servidor", "addServer": "<PERSON><PERSON><PERSON><PERSON>", "addSuccess": "Servidor adicionado com sucesso", "args": "Argumentos", "argsTooltip": "Cada argumento em uma linha", "baseUrlTooltip": "Endereço de URL remoto", "command": "Comand<PERSON>", "config_description": "Configurar modelo de protocolo de contexto do servidor", "deleteError": "Falha ao excluir servidor", "deleteSuccess": "Servidor excluído com sucesso", "dependenciesInstall": "Instalar dependências", "dependenciesInstalling": "Instalando dependências...", "description": "Descrição", "duplicateName": "Já existe um servidor com o mesmo nome", "editJson": "<PERSON>ar <PERSON>", "editServer": "<PERSON><PERSON> servid<PERSON>", "env": "Variáveis de ambiente", "envTooltip": "Formato: CHAVE=valor, uma por linha", "findMore": "<PERSON><PERSON>r<PERSON> MC<PERSON>", "install": "Instalar", "installError": "Falha ao instalar dependências", "installSuccess": "Dependências instaladas com sucesso", "jsonFormatError": "Erro de formatação JSON", "jsonModeHint": "Edite a representação JSON da configuração do servidor MCP. Certifique-se de que o formato está correto antes de salvar.", "jsonSaveError": "Falha ao salvar configuração JSON", "jsonSaveSuccess": "Configuração JSON salva com sucesso", "missingDependencies": "Ausente, instale para continuar", "name": "Nome", "noServers": "Nenhum servidor configurado", "npx_list": {"actions": "Ações", "description": "Descrição", "no_packages": "Nenhum pacote encontrado", "npm": "NPM", "package_name": "Nome do Pacote", "scope_placeholder": "Insira o escopo npm (por exemplo, @sua-organizacao)", "scope_required": "Insira o escopo npm", "search": "<PERSON><PERSON><PERSON><PERSON>", "search_error": "Falha na pesquisa", "usage": "<PERSON><PERSON>", "version": "Vers<PERSON>"}, "serverPlural": "<PERSON><PERSON><PERSON>", "serverSingular": "<PERSON><PERSON><PERSON>", "title": "Servidores MCP", "type": "Tipo", "updateError": "<PERSON><PERSON>ha ao atualizar servidor", "updateSuccess": "Servidor atualizado com sucesso", "url": "URL", "errors": {"32000": "Falha ao iniciar o servidor MCP, verifique se todos os parâmetros foram preenchidos corretamente conforme o tutorial"}, "tabs": {"general": "G<PERSON>", "description": "Descrição", "tools": "Ferramentas", "prompts": "Prompts", "resources": "Recursos"}, "tools": {"inputSchema": "Esquema de Entrada", "availableTools": "Ferramentas Disponíveis", "noToolsAvailable": "Nenhuma Ferramenta Disponível", "loadError": "Falha ao Obter Ferramentas"}, "prompts": {"availablePrompts": "Dicas disponíveis", "noPromptsAvailable": "Nenhuma dica disponível", "arguments": "Argumentos", "requiredField": "Campo obrigatório", "genericError": "Erro ao buscar dicas", "loadError": "Falha ao carregar dicas"}, "resources": {"noResourcesAvailable": "Nenhum recurso disponível", "availableResources": "Recursos disponíveis", "uri": "URI", "mimeType": "Tipo MIME", "size": "<PERSON><PERSON><PERSON>", "blob": "<PERSON><PERSON> biná<PERSON>", "blobInvisible": "Ocultar dados binários", "text": "Texto"}, "types": {"inMemory": "Integrado", "sse": "SSE", "streamableHttp": "Streaming", "stdio": "STDIO"}, "sync": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectProvider": "Selecione o provedor:", "discoverMcpServers": "Descobrir servidores MCP", "discoverMcpServersDescription": "Acesse a plataforma para descobrir servidores MCP disponíveis", "getToken": "Obter token de API", "getTokenDescription": "Obtenha um token de API pessoal da sua conta", "setToken": "Digite seu token", "tokenRequired": "Token de API é obrigatório", "tokenPlaceholder": "Digite o token de API aqui", "button": "Sincronizar", "error": "Erro ao sincronizar servidor MCP", "success": "Servidor MCP sincronizado com sucesso", "unauthorized": "Sincronização não autorizada", "noServersAvailable": "Nenhum servidor MCP disponível"}, "sse": "Eventos do Servidor (sse)", "streamableHttp": "HTTP Transmitido em Fluxo (streamableHttp)", "stdio": "Entrada/Saída <PERSON> (stdio)", "inMemory": "Na Memória", "headers": "Cabeçalhos da Requisição", "headersTooltip": "Cabeçalhos HTTP personalizados para as requisições", "searchNpx": "Buscar MCP", "newServer": "Servidor MCP", "startError": "Falha ao Iniciar", "editMcpJson": "Editar Configura<PERSON> MCP", "installHelp": "Obter Ajuda com a Instalação", "deleteServer": "Excluir <PERSON>", "deleteServerConfirm": "Tem certeza de que deseja excluir este servidor?", "registry": "Fonte de Gerenciamento de Pacotes", "registryTooltip": "Selecione uma fonte alternativa para instalar pacotes, caso tenha problemas de rede com a fonte padrão.", "registryDefault": "Padrão", "not_support": "Modelo <PERSON>portado", "user": "<PERSON><PERSON><PERSON><PERSON>", "system": "Sistema", "timeout": "Tempo Limite", "timeoutTooltip": "Tempo limite (em segundos) para as requisições deste servidor; o padrão é 60 segundos", "provider": "Fornecedor", "providerUrl": "URL do Fornecedor", "logoUrl": "URL do Logotipo", "tags": "Etiquetas", "tagsPlaceholder": "Digite as etiquetas", "providerPlaceholder": "Nome do Fornecedor", "advancedSettings": "Configurações Avançadas"}, "messages.divider": "Divisor de mensagens", "messages.divider.tooltip": "Não aplicável a mensagens de estilo bolha", "messages.grid_columns": "Número de colunas da grade de mensagens", "messages.grid_popover_trigger": "Disparador <PERSON><PERSON> da <PERSON>", "messages.grid_popover_trigger.click": "Clique para mostrar", "messages.grid_popover_trigger.hover": "Passe o mouse para mostrar", "messages.input.paste_long_text_as_file": "Colar texto longo como arquivo", "messages.input.paste_long_text_threshold": "Limite de texto longo", "messages.input.send_shortcuts": "Atalhos de envio", "messages.input.show_estimated_tokens": "Mostrar número estimado de tokens", "messages.input.title": "Configurações de entrada", "messages.markdown_rendering_input_message": "Renderização de markdown na entrada de mensagens", "messages.math_engine": "Motor de fórmulas matemáticas", "messages.metrics": "Atraso inicial {{time_first_token_millsec}}ms | Taxa de token por segundo {{token_speed}} tokens", "messages.model.title": "Configurações de modelo", "messages.navigation": "Botão de navegação de conversa", "messages.navigation.anchor": "Ancoragem de conversa", "messages.navigation.buttons": "Botões de cima e de baixo", "messages.navigation.none": "<PERSON><PERSON>", "messages.title": "Configurações de mensagem", "messages.use_serif_font": "Usar fonte serif", "model": "<PERSON><PERSON>", "models.add.add_model": "Adicionar <PERSON>o", "models.add.group_name": "Nome do grupo", "models.add.group_name.placeholder": "Exemplo: ChatGPT", "models.add.group_name.tooltip": "Exemplo: ChatGPT", "models.add.model_id": "ID do modelo", "models.add.model_id.placeholder": "Obrigatório Exemplo: gpt-3.5-turbo", "models.add.model_id.tooltip": "Exemplo: gpt-3.5-turbo", "models.add.model_name": "Nome do modelo", "models.add.model_name.placeholder": "Exemplo: GPT-3.5", "models.check.all": "Todos", "models.check.all_models_passed": "Todos os modelos passaram na verificação", "models.check.button_caption": "Verificação de saúde", "models.check.disabled": "Desabilitado", "models.check.enable_concurrent": "Verificação concorrente", "models.check.enabled": "Habilitado", "models.check.failed": "Fal<PERSON>", "models.check.keys_status_count": "Passou: {{count_passed}} chaves, falhou: {{count_failed}} chaves", "models.check.model_status_summary": "{{provider}}: {{count_passed}} modelos completaram a verificação de saúde (entre eles, {{count_partial}} modelos não podem ser acessados com algumas chaves), {{count_failed}} modelos não podem ser acessados completamente.", "models.check.no_api_keys": "Nenhuma chave API encontrada, adicione uma chave API primeiro.", "models.check.passed": "Passou", "models.check.select_api_key": "Selecione a chave API a ser usada:", "models.check.single": "Individual", "models.check.start": "<PERSON><PERSON><PERSON>", "models.check.title": "Verificação de saúde do modelo", "models.check.use_all_keys": "Use chaves", "models.default_assistant_model": "Modelo de assistente padrão", "models.default_assistant_model_description": "Modelo usado ao criar um novo assistente, se o assistente não tiver um modelo definido, este será usado", "models.empty": "Sem modelos", "models.enable_topic_naming": "Renomeação automática de tópicos", "models.manage.add_whole_group": "Adicionar todo o grupo", "models.manage.remove_whole_group": "Remover todo o grupo", "models.topic_naming_model": "Modelo de nomenclatura de tópicos", "models.topic_naming_model_description": "Modelo usado para nomear tópicos automaticamente", "models.topic_naming_model_setting_title": "Configurações do modelo de nomenclatura de tópicos", "models.topic_naming_prompt": "Prompt de nomenclatura de tópicos", "models.translate_model": "Modelo de tradução", "models.translate_model_description": "Modelo usado para serviços de tradução", "models.translate_model_prompt_message": "Digite o prompt do modelo de tradução", "models.translate_model_prompt_title": "Prompt do modelo de tradução", "moresetting": "Configurações adicionais", "moresetting.check.confirm": "Confirma<PERSON>", "moresetting.check.warn": "Por favor, selecione com cuidado esta opção, uma seleção incorreta pode impedir o uso normal dos modelos!!!", "moresetting.warn": "Aviso de risco", "provider": {"add.name": "Nome do Fornecedor", "add.name.placeholder": "Exemplo OpenAI", "add.title": "<PERSON><PERSON><PERSON><PERSON>", "add.type": "<PERSON><PERSON><PERSON> de Fornecedor", "api.url.preview": "Pré-visualização: {{url}}", "api.url.reset": "Redefinir", "api.url.tip": "Ignorar v1 na versão finalizada com /, usar endereço de entrada forçado se terminar com #", "api_host": "Endereço API", "api_key": "Chave API", "api_key.tip": "Use vírgula para separar várias chaves", "api_version": "Vers<PERSON> da API", "charge": "<PERSON><PERSON><PERSON><PERSON>", "check": "Verificar", "check_all_keys": "<PERSON><PERSON><PERSON><PERSON> todas as chaves", "check_multiple_keys": "Verificar várias chaves API", "copilot": {"auth_failed": "Falha na autenticação do Github Copilot", "auth_success": "Autenticação do Github Copilot bem-sucedida", "auth_success_title": "Autenticação bem-sucedida", "code_failed": "Falha ao obter Código do Dispositivo, tente novamente", "code_generated_desc": "Por favor, copie o Código do Dispositivo para o link do navegador abaixo", "code_generated_title": "Obter Código do Dispositivo", "confirm_login": "O uso excessivo pode resultar no bloqueio da sua conta do Github, use com cuidado!!!!", "confirm_title": "Aviso de Risco", "connect": "Conectar ao Github", "custom_headers": "Cabeçalhos Personalizados", "description": "Sua conta do Github precisa assinar o Copilot", "expand": "Expandir", "headers_description": "Cabeçalhos personalizados (formato json)", "invalid_json": "Formato JSON inválido", "login": "Fazer login no G<PERSON>ub", "logout": "<PERSON><PERSON> <PERSON>", "logout_failed": "Falha ao sair, tente novamente", "logout_success": "Saiu com sucesso", "model_setting": "Configuração do Modelo", "open_verification_first": "Por favor, clique no link acima para acessar a página de verificação", "rate_limit": "Limite de Taxa", "tooltip": "Para usar o Github Copilot, você precisa fazer login no Github"}, "delete.content": "Tem certeza de que deseja excluir este fornecedor de modelo?", "delete.title": "Excluir Fornecedor", "docs_check": "Verificar", "docs_more_details": "Obter mais de<PERSON>hes", "get_api_key": "Clique aqui para obter a chave", "is_not_support_array_content": "Ativar modo compatível", "not_checked": "Não verificado", "remove_duplicate_keys": "Remover chaves duplicadas", "remove_invalid_keys": "Remover chaves inválidas", "search": "Procurar plataforma de modelos...", "search_placeholder": "Procurar ID ou nome do modelo", "title": "Serviços de Modelos", "oauth": {"button": "Entrar com a conta {{provider}}", "description": "Este serviço é fornecido por <website>{{provider}}</website>", "official_website": "Site Oficial"}, "notes": {"title": "Observação do Modelo", "placeholder": "Por favor, insira o conteúdo no formato Markdown...", "markdown_editor_default_value": "Área de Visualização"}, "basic_auth": "Autenticação HTTP", "basic_auth.tip": "Aplica-se a instâncias implantadas por meio de servidor (consulte a documentação). Atualmente, apenas o esquema Basic é suportado (RFC7617).", "basic_auth.user_name": "Nome de usuário", "basic_auth.user_name.tip": "Deixe em branco para desativar", "basic_auth.password": "<PERSON><PERSON>", "bills": "<PERSON><PERSON>", "no_models_for_check": "Não há modelos disponíveis para verificação (por exemplo, modelos de conversa)"}, "proxy": {"mode": {"custom": "Proxy Personalizado", "none": "Não Usar Proxy", "system": "Proxy do Sistema", "title": "Modo de Proxy"}, "title": "Configurações de Proxy"}, "proxy.title": "Endereço de proxy", "quickAssistant": {"click_tray_to_show": "Clique no ícone da bandeja para iniciar", "enable_quick_assistant": "Ativar assistente r<PERSON>o", "read_clipboard_at_startup": "Ler área de transferência ao iniciar", "title": "Assistente Rápido", "use_shortcut_to_show": "Clique com o botão direito no ícone da bandeja ou use atalhos para iniciar"}, "shortcuts": {"action": "Ação", "clear_shortcut": "<PERSON><PERSON>", "clear_topic": "Limpar mensagem", "copy_last_message": "Copiar a última mensagem", "key": "Tecla", "mini_window": "Atalho de assistente", "new_topic": "Novo tópico", "press_shortcut": "Pressionar atalho", "reset_defaults": "Redefinir atalhos padrão", "reset_defaults_confirm": "Tem certeza de que deseja redefinir todos os atalhos?", "reset_to_default": "Redefinir para padrão", "search_message": "Pesquisar mensagem", "show_app": "Exibir ap<PERSON>ti<PERSON>", "show_settings": "<PERSON><PERSON>r configuraç<PERSON><PERSON>", "title": "Atalhos", "toggle_new_context": "Limpar <PERSON>", "toggle_show_assistants": "Alternar exibição de assistentes", "toggle_show_topics": "Alternar exibição de tópicos", "zoom_in": "Ampliar interface", "zoom_out": "Diminuir interface", "zoom_reset": "Redefinir zoom"}, "theme.system": "Sistema", "theme.dark": "Escuro", "theme.light": "<PERSON><PERSON><PERSON>", "theme.title": "<PERSON><PERSON>", "theme.window.style.opaque": "<PERSON>la op<PERSON>", "theme.window.style.title": "<PERSON><PERSON><PERSON>", "theme.window.style.transparent": "<PERSON><PERSON>", "title": "Configurações", "topic.position": "Posição do tópico", "topic.position.left": "E<PERSON>rda", "topic.position.right": "<PERSON><PERSON><PERSON>", "topic.show.time": "Mostrar tempo do tópico", "tray.onclose": "Minimizar para bandeja ao fechar", "tray.show": "Mostrar ícone de bandeja", "tray.title": "Tray", "websearch": {"blacklist": "Lista Negra", "blacklist_description": "Os seguintes sites não aparecerão nos resultados da pesquisa", "blacklist_tooltip": "Por favor, use o seguinte formato (separado por quebras de linha)\\nexample.com \\\\:nhttps://www.example.com \\\\:nhttps://example.com \\\\:n*://*.example.com", "check": "Verificar", "check_failed": "Verificação falhou", "check_success": "Verificação bem-sucedida", "get_api_key": "Clique aqui para obter a chave", "no_provider_selected": "Selecione um provedor de pesquisa antes de verificar", "search_max_result": "Número de resultados da pesquisa", "search_provider": "<PERSON><PERSON><PERSON>es<PERSON>", "search_provider_placeholder": "Selecione um provedor de pesquisa", "search_result_default": "Padrão", "search_with_time": "Pesquisar com data", "tavily": {"api_key": "Chave de <PERSON> do <PERSON>", "api_key.placeholder": "Insira a chave de API do Tavily", "description": "O Tavily é um mecanismo de busca projetado especificamente para agentes de IA, oferecendo resultados em tempo real, precisos, sugestões inteligentes de consulta e capacidades de pesquisa aprofundada", "title": "<PERSON><PERSON>"}, "title": "Pesquisa na Web", "overwrite": "Substituir <PERSON><PERSON> de pesquisa", "overwrite_tooltip": "Forçar o uso do provedor de pesquisa em vez do modelo de linguagem grande para pesquisas", "subscribe": "<PERSON><PERSON><PERSON> lista negra", "subscribe_update": "Atual<PERSON>r agora", "subscribe_add": "Adicionar assinatura", "subscribe_url": "Endereço da fonte de assinatura", "subscribe_name": "Nome alternativo", "subscribe_name.placeholder": "Nome alternativo usado quando a fonte assinada não tem nome", "subscribe_add_success": "Fonte de assinatura adicionada com sucesso!", "subscribe_delete": "Excluir fonte de assinatura", "apikey": "Chave API", "free": "<PERSON><PERSON><PERSON><PERSON>", "content_limit": "Limite de comprimento do conteúdo", "content_limit_tooltip": "Limita o comprimento do conteúdo nos resultados da pesquisa; conteúdo excedente será truncado"}, "miniapps": {"open_link_external": {"title": "Abrir link em nova janela do navegador"}, "custom": {"title": "Aplicativo Personalizado", "edit_title": "Editar Aplicativo Personalizado", "save_success": "Aplicativo personalizado salvo com sucesso.", "save_error": "Falha ao salvar o aplicativo personalizado.", "remove_success": "Aplicativo personalizado excluído com sucesso.", "remove_error": "Falha ao excluir o aplicativo personalizado.", "logo_upload_success": "Logo enviada com sucesso.", "logo_upload_error": "<PERSON>alha no envio da Logo.", "id": "ID", "id_error": "A ID é obrigatória.", "id_placeholder": "Digite a ID", "name": "Nome", "name_error": "O nome é obrigatório.", "name_placeholder": "Digite o nome", "url": "URL", "url_error": "A URL é obrigatória.", "url_placeholder": "Digite a URL", "logo": "Logo", "logo_url": "URL da Logo", "logo_file": "Enviar Arquivo da Logo", "logo_url_label": "URL da Logo", "logo_url_placeholder": "Digite a URL da Logo", "logo_upload_label": "Enviar Logo", "logo_upload_button": "Enviar", "save": "<PERSON><PERSON>", "edit_description": "Edite aqui as configurações do aplicativo personalizado. Cada aplicativo deve conter os campos id, name, url e logo.", "placeholder": "Digite a configuração do aplicativo personalizado (formato JSON)", "duplicate_ids": "IDs duplicadas encontradas: {{ids}}", "conflicting_ids": "Conflito com IDs padrão: {{ids}}"}, "title": "Configurações do Mini Aplicativo", "disabled": "Mini Aplicativos Ocultos", "empty": "Arraste para cá os mini aplicativos que deseja ocultar", "visible": "Mini Aplicativos Visíveis", "cache_settings": "Configurações de Cache", "cache_title": "Quantidade de Mini Aplicativos no Cache", "cache_description": "Defina o número máximo de mini aplicativos que permanecerão ativos simultaneamente", "reset_tooltip": "Redefinir para os valores padrão", "display_title": "Configurações de Exibição dos Mini Aplicativos", "sidebar_title": "Exibição de Mini Aplicativos Ativos na Barra Lateral", "sidebar_description": "Defina se os mini aplicativos ativos serão exibidos na barra lateral", "cache_change_notice": "As alterações entrarão em vigor após a abertura ou remoção dos mini aplicativos até atingir o número definido"}, "quickPhrase": {"title": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "contentLabel": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Por favor, insira o título da frase", "contentPlaceholder": "Por favor, insira o conteúdo da frase. É permitido usar variáveis, e em seguida pressionar a tecla Tab para localizar rapidamente as variáveis e editá-las. Por exemplo:\\nPlaneje uma rota de ${from} para ${to} e envie para ${email}.", "delete": "Excluir Frase", "deleteConfirm": "A frase excluída não poderá ser recuperada. Deseja continuar?", "locationLabel": "Adicionar Localização", "global": "Frase Global", "assistant": "Frase do Assistente"}, "quickPanel": {"title": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "select": "Selecionar", "page": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmar", "back": "Voltar", "forward": "<PERSON><PERSON><PERSON><PERSON>", "multiple": "Múltipla Seleção"}, "privacy": {"title": "Configurações de Privacidade", "enable_privacy_mode": "Enviar relatórios de erro e estatísticas de forma anônima"}, "assistant.icon.type": "Tipo de ícone do modelo", "assistant.icon.type.model": "Ícone do modelo", "assistant.icon.type.emoji": "<PERSON><PERSON><PERSON>", "assistant.icon.type.none": "<PERSON><PERSON>", "general.auto_check_update.title": "Atualização automática", "input.show_translate_confirm": "Mostrar diálogo de confirmação de tradução", "messages.prompt": "<PERSON><PERSON><PERSON> pala<PERSON>-chave", "messages.input.enable_quick_triggers": "Ativar menu rápido com '/' e '@'", "messages.input.enable_delete_model": "Ativar tecla de exclusão para remover modelos/anexos inseridos", "messages.math_engine.none": "<PERSON><PERSON><PERSON>", "models.manage.add_listed": "Adicionar <PERSON>o da <PERSON>a", "models.manage.remove_listed": "Remover modelo da lista", "zoom.title": "Zoom da página"}, "translate": {"any.language": "qualquer idioma", "button.translate": "Traduzir", "close": "<PERSON><PERSON><PERSON>", "confirm": {"content": "A tradução substituirá o texto original, deseja continuar?", "title": "Confirmação de Tradução"}, "error.failed": "Tradução falhou", "error.not_configured": "Modelo de tradução não configurado", "history": {"clear": "<PERSON><PERSON>", "clear_description": "Limpar histórico irá deletar todos os registros de tradução. Deseja continuar?", "delete": "Excluir", "empty": "Nenhum histórico de tradução disponível", "title": "Histórico de Tradução"}, "input.placeholder": "Digite o texto para traduzir", "output.placeholder": "Tradução", "processing": "Traduzindo...", "scroll_sync.disable": "Desativar sincronização de rolagem", "scroll_sync.enable": "Ativar sincronização de rolagem", "title": "Tradução", "tooltip.newline": "<PERSON><PERSON> de linha", "menu": {"description": "Traduzir o conteúdo da caixa de entrada atual"}}, "tray": {"quit": "<PERSON><PERSON>", "show_mini_window": "Atalho de Assistente", "show_window": "<PERSON><PERSON><PERSON>"}, "words": {"knowledgeGraph": "Gráfico de Conhecimento", "quit": "<PERSON><PERSON>", "show_window": "<PERSON><PERSON><PERSON>", "visualization": "Visualização"}, "update": {"title": "Atualização", "message": "Nova versão {{version}} disponível, deseja instalar agora?", "later": "<PERSON><PERSON> tarde", "install": "Instalar", "noReleaseNotes": "Sem notas de versão"}}}
name: ❓ 提问 & 讨论 (中文)
description: 寻求帮助、讨论问题、提出疑问等...
title: '[讨论]: '
labels: ['kind/question']
body:
  - type: markdown
    attributes:
      value: |
        感谢您的提问！请尽可能详细地描述您的问题，这样我们才能更好地帮助您。

  - type: checkboxes
    id: checklist
    attributes:
      label: Issue 检查清单
      description: |
        在提交 Issue 前请确保您已经完成了以下所有步骤
      options:
        - label: 我理解 Issue 是用于反馈和解决问题的，而非吐槽评论区，将尽可能提供更多信息帮助问题解决。
          required: true
        - label: 我确认自己需要的是提出问题并且讨论问题，而不是 Bug 反馈或需求建议。
          required: true

  - type: dropdown
    id: platform
    attributes:
      label: 平台
      description: 您正在使用哪个平台？
      options:
        - Windows
        - macOS
        - Linux
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: 版本
      description: 您正在运行的 Cherry Studio 版本是什么？
      placeholder: 例如 v1.0.0
    validations:
      required: true

  - type: textarea
    id: question
    attributes:
      label: 您的问题
      description: 请详细描述您的问题
      placeholder: 请尽可能清楚地说明您的问题...
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: 相关背景
      description: 请提供一些背景信息，帮助我们更好地理解您的问题
      placeholder: 例如：使用场景、已尝试的解决方案等

  - type: textarea
    id: additional
    attributes:
      label: 补充信息
      description: 任何其他相关的信息、截图或代码示例
      render: shell

  - type: dropdown
    id: priority
    attributes:
      label: 优先级
      description: 这个问题对您来说有多紧急？
      options:
        - 低 (有空再看)
        - 中 (希望尽快得到答复)
        - 高 (阻碍工作进行)
    validations:
      required: true

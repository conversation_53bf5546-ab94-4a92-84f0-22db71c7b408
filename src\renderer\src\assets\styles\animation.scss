@keyframes animation-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color), 0.5);
  }
  70% {
    box-shadow: 0 0 0 var(--pulse-size) rgba(var(--pulse-color), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color), 0);
  }
}

// 电磁波扩散效果
.animation-pulse {
  --pulse-color: 59, 130, 246;
  --pulse-size: 8px;
  animation: animation-pulse 1.5s infinite;
}

// Modal动画
@keyframes animation-move-down-in {
  0% {
    transform: translate3d(0, 100%, 0);
    transform-origin: 0 0;
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    transform-origin: 0 0;
    opacity: 1;
  }
}
@keyframes animation-move-down-out {
  0% {
    transform: translate3d(0, 0, 0);
    transform-origin: 0 0;
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 100%, 0);
    transform-origin: 0 0;
    opacity: 0;
  }
}
.animation-move-down-enter,
.animation-move-down-appear {
  animation-name: animation-move-down-in;
  animation-fill-mode: both;
  animation-duration: 0.25s;
}
.animation-move-down-leave {
  animation-name: animation-move-down-out;
  animation-fill-mode: both;
  animation-duration: 0.25s;
}

@use './font.scss';

html {
  font-family: var(--font-family);
}

:root {
  // Basic Colors
  --color-primary: #00b96b;
  --color-error: #f44336;

  --selection-toolbar-color-primary: var(--color-primary);
  --selection-toolbar-color-error: var(--color-error);

  // Toolbar
  --selection-toolbar-height: 36px; // default: 36px   max: 42px
  --selection-toolbar-font-size: 14px; // default: 14px

  --selection-toolbar-logo-display: flex; // values: flex | none
  --selection-toolbar-logo-size: 22px; // default: 22px
  --selection-toolbar-logo-margin: 0 0 0 5px; // default: 0 0 05px

  // DO NOT MODIFY THESE VALUES, IF YOU DON'T KNOW WHAT YOU ARE DOING
  --selection-toolbar-padding: 2px 4px 2px 2px; // default: 2px 4px 2px 2px
  --selection-toolbar-margin: 2px 3px 5px 3px; // default: 2px 3px 5px 3px
  // ------------------------------------------------------------

  --selection-toolbar-border-radius: 6px;
  --selection-toolbar-border: 1px solid rgba(55, 55, 55, 0.5);
  --selection-toolbar-box-shadow: 0px 2px 3px rgba(50, 50, 50, 0.3);
  --selection-toolbar-background: rgba(20, 20, 20, 0.95);

  // Buttons

  --selection-toolbar-button-icon-size: 16px; // default: 16px
  --selection-toolbar-button-text-margin: 0 0 0 3px; // default: 0 0 0 3px
  --selection-toolbar-button-margin: 0 2px; // default: 0 2px
  --selection-toolbar-button-padding: 4px 6px; // default: 4px 6px
  --selection-toolbar-button-border-radius: 4px; // default: 4px
  --selection-toolbar-button-border: none; // default: none
  --selection-toolbar-button-box-shadow: none; // default: none

  --selection-toolbar-button-text-color: rgba(255, 255, 245, 0.9);
  --selection-toolbar-button-icon-color: var(--selection-toolbar-button-text-color);
  --selection-toolbar-button-text-color-hover: var(--selection-toolbar-color-primary);
  --selection-toolbar-button-icon-color-hover: var(--selection-toolbar-color-primary);
  --selection-toolbar-button-bgcolor: transparent; // default: transparent
  --selection-toolbar-button-bgcolor-hover: #222222;
}

[theme-mode='light'] {
  --selection-toolbar-border: 1px solid rgba(200, 200, 200, 0.5);
  --selection-toolbar-box-shadow: 0px 2px 3px rgba(50, 50, 50, 0.3);
  --selection-toolbar-background: rgba(245, 245, 245, 0.95);

  --selection-toolbar-button-text-color: rgba(0, 0, 0, 1);
  --selection-toolbar-button-icon-color: var(--selection-toolbar-button-text-color);
  --selection-toolbar-button-text-color-hover: var(--selection-toolbar-color-primary);
  --selection-toolbar-button-icon-color-hover: var(--selection-toolbar-color-primary);
  --selection-toolbar-button-bgcolor-hover: rgba(0, 0, 0, 0.04);
}

default-mode:
  add:
  remove: [pull_request_target, issues]

labels:
  # <!-- [Ss]kip `LABEL` --> 跳过一个 label
  # <!-- [Rr]emove `LABEL` --> 去掉一个 label

  # skips and removes
  - name: skip all
    content:
    regexes: "[Ss]kip (?:[Aa]ll |)[Ll]abels?"
  - name: remove all
    content:
    regexes: "[Rr]emove (?:[Aa]ll |)[Ll]abels?"

  - name: skip kind/bug
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)kind/bug(?:`|)"
  - name: remove kind/bug
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)kind/bug(?:`|)"

  - name: skip kind/enhancement
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)kind/enhancement(?:`|)"
  - name: remove kind/enhancement
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)kind/enhancement(?:`|)"

  - name: skip kind/question
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)kind/question(?:`|)"
  - name: remove kind/question
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)kind/question(?:`|)"

  - name: skip area/Connectivity
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)area/Connectivity(?:`|)"
  - name: remove area/Connectivity
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)area/Connectivity(?:`|)"

  - name: skip area/UI/UX
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)area/UI/UX(?:`|)"
  - name: remove area/UI/UX
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)area/UI/UX(?:`|)"

  - name: skip kind/documentation
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)kind/documentation(?:`|)"
  - name: remove kind/documentation
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)kind/documentation(?:`|)"

  - name: skip client:linux
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)client:linux(?:`|)"
  - name: remove client:linux
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)client:linux(?:`|)"

  - name: skip client:mac
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)client:mac(?:`|)"
  - name: remove client:mac
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)client:mac(?:`|)"

  - name: skip client:win
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)client:win(?:`|)"
  - name: remove client:win
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)client:win(?:`|)"
  
  - name: skip sig/Assistant
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)sig/Assistant(?:`|)"
  - name: remove sig/Assistant
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)sig/Assistant(?:`|)"

  - name: skip sig/Data
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)sig/Data(?:`|)"
  - name: remove sig/Data
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)sig/Data(?:`|)"

  - name: skip sig/MCP
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)sig/MCP(?:`|)"
  - name: remove sig/MCP
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)sig/MCP(?:`|)"

  - name: skip sig/RAG
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)sig/RAG(?:`|)"
  - name: remove sig/RAG
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)sig/RAG(?:`|)"

  - name: skip lgtm
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)lgtm(?:`|)"
  - name: remove lgtm
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)lgtm(?:`|)"

  - name: skip License
    content:
    regexes: "[Ss]kip (?:[Ll]abels? |)(?:`|)License(?:`|)"
  - name: remove License
    content:
    regexes: "[Rr]emove (?:[Ll]abels? |)(?:`|)License(?:`|)"

  # `Dev Team`
  - name: Dev Team
    mode:
      add: [pull_request_target, issues]
    author_association:
      - COLLABORATOR

  # Area labels
  - name: area/Connectivity
    content: area/Connectivity
    regexes: "代理|[Pp]roxy"
    skip-if:
      - skip all
      - skip area/Connectivity
    remove-if:
      - remove all
      - remove area/Connectivity

  - name: area/UI/UX
    content: area/UI/UX
    regexes: "界面|[Uu][Ii]|重叠|按钮|图标|组件|渲染|菜单|栏目|头像|主题|样式|[Cc][Ss][Ss]"
    skip-if:
      - skip all
      - skip area/UI/UX
    remove-if:
      - remove all
      - remove area/UI/UX

  # Kind labels
  - name: kind/documentation
    content: kind/documentation
    regexes: "文档|教程|[Dd]oc(s|umentation)|[Rr]eadme"
    skip-if:
      - skip all
      - skip kind/documentation
    remove-if:
      - remove all
      - remove kind/documentation

  # Client labels
  - name: client:linux
    content: client:linux
    regexes: "(?:[Ll]inux|[Uu]buntu|[Dd]ebian)"
    skip-if:
      - skip all
      - skip client:linux
    remove-if:
      - remove all
      - remove client:linux

  - name: client:mac
    content: client:mac
    regexes: "(?:[Mm]ac|[Mm]acOS|[Oo]SX)"
    skip-if:
      - skip all
      - skip client:mac
    remove-if:
      - remove all
      - remove client:mac

  - name: client:win
    content: client:win
    regexes: "(?:[Ww]in|[Ww]indows)"
    skip-if:
      - skip all
      - skip client:win
    remove-if:
      - remove all
      - remove client:win

  # SIG labels
  - name: sig/Assistant
    content: sig/Assistant
    regexes: "快捷助手|[Aa]ssistant"
    skip-if:
      - skip all
      - skip sig/Assistant
    remove-if:
      - remove all
      - remove sig/Assistant

  - name: sig/Data
    content: sig/Data
    regexes: "[Ww]ebdav|坚果云|备份|同步|数据|Obsidian|Notion|Joplin|思源"
    skip-if:
      - skip all
      - skip sig/Data
    remove-if:
      - remove all
      - remove sig/Data

  - name: sig/MCP
    content: sig/MCP
    regexes: "[Mm][Cc][Pp]"
    skip-if:
      - skip all
      - skip sig/MCP
    remove-if:
      - remove all
      - remove sig/MCP

  - name: sig/RAG
    content: sig/RAG
    regexes: "知识库|[Rr][Aa][Gg]"
    skip-if:
      - skip all
      - skip sig/RAG
    remove-if:
      - remove all
      - remove sig/RAG

  # Other labels
  - name: lgtm
    content: lgtm
    regexes: "(?:[Ll][Gg][Tt][Mm]|[Ll]ooks [Gg]ood [Tt]o [Mm]e)"
    skip-if:
      - skip all
      - skip lgtm
    remove-if:
      - remove all
      - remove lgtm

  - name: License
    content: License
    regexes: "(?:[Ll]icense|[Cc]opyright|[Mm][Ii][Tt]|[Aa]pache)"
    skip-if:
      - skip all
      - skip License
    remove-if:
      - remove all
      - remove License

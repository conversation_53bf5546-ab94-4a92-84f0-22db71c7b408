@use './container.scss';

#inputbar {
  resize: none;
}

.ant-image-preview-switch-left {
  -webkit-app-region: no-drag;
}

.ant-btn:not(:disabled):focus-visible {
  outline: none;
}

.ant-tabs-tabpane:focus-visible {
  outline: none;
}

.ant-tabs-tab-btn {
  outline: none !important;
}

.ant-segmented-group {
  gap: 4px;
}

.minapp-drawer {
  max-width: calc(100vw - var(--sidebar-width));
  .ant-drawer-content-wrapper {
    box-shadow: none;
  }
  .ant-drawer-header {
    position: absolute;
    -webkit-app-region: drag;
    min-height: calc(var(--navbar-height) + 0.5px);
    width: calc(100vw - var(--sidebar-width));
    margin-top: -0.5px;
    border-bottom: none;
  }
  .ant-drawer-body {
    padding: 0;
    margin-top: var(--navbar-height);
    overflow: hidden;
    @extend #content-container;
  }
  .minapp-mask {
    background-color: transparent !important;
  }
}

.ant-drawer-header {
  -webkit-app-region: no-drag;
}

.message-attachments {
  .ant-upload-list-item:hover {
    background-color: initial !important;
  }
}

.ant-dropdown-menu .ant-dropdown-menu-sub {
  max-height: 50vh;
  width: max-content;
  overflow-y: auto;
  overflow-x: hidden;
  border: 0.5px solid var(--color-border);
}
.ant-dropdown {
  background-color: var(--ant-color-bg-elevated);
  overflow: hidden;
  border-radius: var(--ant-border-radius-lg);
  .ant-dropdown-menu {
    max-height: 50vh;
    overflow-y: auto;
    border: 0.5px solid var(--color-border);
  }
  .ant-dropdown-arrow + .ant-dropdown-menu {
    border: none;
  }
}
.ant-select-dropdown {
  border: 0.5px solid var(--color-border);
}
.ant-dropdown-menu-submenu {
  background-color: var(--ant-color-bg-elevated);
  overflow: hidden;
  border-radius: var(--ant-border-radius-lg);
}

.ant-popover {
  .ant-popover-inner {
    border: 0.5px solid var(--color-border);
    .ant-popover-inner-content {
      max-height: 70vh;
      overflow-y: auto;
    }
  }
  .ant-popover-arrow + .ant-popover-content {
    .ant-popover-inner {
      border: none;
    }
  }
}

.ant-modal:not(.ant-modal-confirm) {
  .ant-modal-confirm-body-has-title {
    padding: 16px 0 0 0;
  }
  .ant-modal-content {
    border-radius: 10px;
    border: 0.5px solid var(--color-border);
    padding: 0 0 8px 0;
    .ant-modal-header {
      padding: 16px 16px 0 16px;
      border-radius: 10px;
    }
    .ant-modal-body {
      max-height: 80vh;
      overflow-y: auto;
      padding: 0 16px 0 16px;
    }
    .ant-modal-footer {
      padding: 0 16px 8px 16px;
    }
    .ant-modal-confirm-btns {
      margin-bottom: 8px;
    }
  }
}
.ant-modal.ant-modal-confirm.ant-modal-confirm-confirm {
  .ant-modal-content {
    padding: 16px;
  }
}

.ant-collapse {
  border: 1px solid var(--color-border);
  .ant-color-picker & {
    border: none;
  }
}

.ant-collapse-content {
  border-top: 0.5px solid var(--color-border) !important;
  .ant-color-picker & {
    border-top: none !important;
  }
}

.ant-slider {
  .ant-slider-handle::after {
    box-shadow: 0 1px 4px 0px rgb(128 128 128 / 50%) !important;
  }
}
